// Autogenerated from <PERSON><PERSON> (v21.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import <Foundation/Foundation.h>

@protocol FlutterBinaryMessenger;
@protocol FlutterMessageCodec;
@class FlutterError;
@class FlutterStandardTypedData;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, PigeonSensorPosition) {
  PigeonSensorPositionBack = 0,
  PigeonSensorPositionFront = 1,
  PigeonSensorPositionUnknown = 2,
};

/// Wrapper for PigeonSensorPosition to allow for nullability.
@interface PigeonSensorPositionBox : NSObject
@property(nonatomic, assign) PigeonSensorPosition value;
- (instancetype)initWithValue:(PigeonSensorPosition)value;
@end

/// Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
/// let the device choose the best/worst quality available.
/// [highest] is the default quality.
///
/// Qualities are defined like this:
/// [sd] < [hd] < [fhd] < [uhd]
typedef NS_ENUM(NSUInteger, VideoRecordingQuality) {
  VideoRecordingQualityLowest = 0,
  VideoRecordingQualitySd = 1,
  VideoRecordingQualityHd = 2,
  VideoRecordingQualityFhd = 3,
  VideoRecordingQualityUhd = 4,
  VideoRecordingQualityHighest = 5,
};

/// Wrapper for VideoRecordingQuality to allow for nullability.
@interface VideoRecordingQualityBox : NSObject
@property(nonatomic, assign) VideoRecordingQuality value;
- (instancetype)initWithValue:(VideoRecordingQuality)value;
@end

/// If the specified [VideoRecordingQuality] is not available on the device,
/// the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
/// [higher] is the default fallback strategy.
typedef NS_ENUM(NSUInteger, QualityFallbackStrategy) {
  QualityFallbackStrategyHigher = 0,
  QualityFallbackStrategyLower = 1,
};

/// Wrapper for QualityFallbackStrategy to allow for nullability.
@interface QualityFallbackStrategyBox : NSObject
@property(nonatomic, assign) QualityFallbackStrategy value;
- (instancetype)initWithValue:(QualityFallbackStrategy)value;
@end

typedef NS_ENUM(NSUInteger, CupertinoFileType) {
  CupertinoFileTypeQuickTimeMovie = 0,
  CupertinoFileTypeMpeg4 = 1,
  CupertinoFileTypeAppleM4V = 2,
  CupertinoFileTypeType3GPP = 3,
  CupertinoFileTypeType3GPP2 = 4,
};

/// Wrapper for CupertinoFileType to allow for nullability.
@interface CupertinoFileTypeBox : NSObject
@property(nonatomic, assign) CupertinoFileType value;
- (instancetype)initWithValue:(CupertinoFileType)value;
@end

typedef NS_ENUM(NSUInteger, CupertinoCodecType) {
  CupertinoCodecTypeH264 = 0,
  CupertinoCodecTypeHevc = 1,
  CupertinoCodecTypeHevcWithAlpha = 2,
  CupertinoCodecTypeJpeg = 3,
  CupertinoCodecTypeAppleProRes4444 = 4,
  CupertinoCodecTypeAppleProRes422 = 5,
  CupertinoCodecTypeAppleProRes422HQ = 6,
  CupertinoCodecTypeAppleProRes422LT = 7,
  CupertinoCodecTypeAppleProRes422Proxy = 8,
};

/// Wrapper for CupertinoCodecType to allow for nullability.
@interface CupertinoCodecTypeBox : NSObject
@property(nonatomic, assign) CupertinoCodecType value;
- (instancetype)initWithValue:(CupertinoCodecType)value;
@end

typedef NS_ENUM(NSUInteger, PigeonSensorType) {
  /// A built-in wide-angle camera.
  ///
  /// The wide angle sensor is the default sensor for iOS
  PigeonSensorTypeWideAngle = 0,
  /// A built-in camera with a shorter focal length than that of the wide-angle camera.
  PigeonSensorTypeUltraWideAngle = 1,
  /// A built-in camera device with a longer focal length than the wide-angle camera.
  PigeonSensorTypeTelephoto = 2,
  /// A device that consists of two cameras, one Infrared and one YUV.
  ///
  /// iOS only
  PigeonSensorTypeTrueDepth = 3,
  PigeonSensorTypeUnknown = 4,
};

/// Wrapper for PigeonSensorType to allow for nullability.
@interface PigeonSensorTypeBox : NSObject
@property(nonatomic, assign) PigeonSensorType value;
- (instancetype)initWithValue:(PigeonSensorType)value;
@end

typedef NS_ENUM(NSUInteger, CamerAwesomePermission) {
  CamerAwesomePermissionStorage = 0,
  CamerAwesomePermissionCamera = 1,
  CamerAwesomePermissionLocation = 2,
  CamerAwesomePermissionRecord_audio = 3,
};

/// Wrapper for CamerAwesomePermission to allow for nullability.
@interface CamerAwesomePermissionBox : NSObject
@property(nonatomic, assign) CamerAwesomePermission value;
- (instancetype)initWithValue:(CamerAwesomePermission)value;
@end

typedef NS_ENUM(NSUInteger, AnalysisImageFormat) {
  AnalysisImageFormatYuv_420 = 0,
  AnalysisImageFormatBgra8888 = 1,
  AnalysisImageFormatJpeg = 2,
  AnalysisImageFormatNv21 = 3,
  AnalysisImageFormatUnknown = 4,
};

/// Wrapper for AnalysisImageFormat to allow for nullability.
@interface AnalysisImageFormatBox : NSObject
@property(nonatomic, assign) AnalysisImageFormat value;
- (instancetype)initWithValue:(AnalysisImageFormat)value;
@end

typedef NS_ENUM(NSUInteger, AnalysisRotation) {
  AnalysisRotationRotation0deg = 0,
  AnalysisRotationRotation90deg = 1,
  AnalysisRotationRotation180deg = 2,
  AnalysisRotationRotation270deg = 3,
};

/// Wrapper for AnalysisRotation to allow for nullability.
@interface AnalysisRotationBox : NSObject
@property(nonatomic, assign) AnalysisRotation value;
- (instancetype)initWithValue:(AnalysisRotation)value;
@end

@class PreviewSize;
@class ExifPreferences;
@class PigeonSensor;
@class VideoOptions;
@class AndroidVideoOptions;
@class CupertinoVideoOptions;
@class PigeonSensorTypeDevice;
@class AndroidFocusSettings;
@class PlaneWrapper;
@class CropRectWrapper;
@class AnalysisImageWrapper;

@interface PreviewSize : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithWidth:(double )width
    height:(double )height;
@property(nonatomic, assign) double  width;
@property(nonatomic, assign) double  height;
@end

@interface ExifPreferences : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithSaveGPSLocation:(BOOL )saveGPSLocation;
@property(nonatomic, assign) BOOL  saveGPSLocation;
@end

@interface PigeonSensor : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithPosition:(PigeonSensorPosition)position
    type:(PigeonSensorType)type
    deviceId:(nullable NSString *)deviceId;
@property(nonatomic, assign) PigeonSensorPosition position;
@property(nonatomic, assign) PigeonSensorType type;
@property(nonatomic, copy, nullable) NSString * deviceId;
@end

/// Video recording options. Some of them are specific to each platform.
@interface VideoOptions : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithEnableAudio:(BOOL )enableAudio
    quality:(nullable VideoRecordingQualityBox *)quality
    android:(nullable AndroidVideoOptions *)android
    ios:(nullable CupertinoVideoOptions *)ios;
/// Enable audio while video recording
@property(nonatomic, assign) BOOL  enableAudio;
/// The quality of the video recording, defaults to [VideoRecordingQuality.highest].
@property(nonatomic, strong, nullable) VideoRecordingQualityBox * quality;
@property(nonatomic, strong, nullable) AndroidVideoOptions * android;
@property(nonatomic, strong, nullable) CupertinoVideoOptions * ios;
@end

@interface AndroidVideoOptions : NSObject
+ (instancetype)makeWithBitrate:(nullable NSNumber *)bitrate
    fallbackStrategy:(nullable QualityFallbackStrategyBox *)fallbackStrategy;
/// The bitrate of the video recording. Only set it if a custom bitrate is
/// desired.
@property(nonatomic, strong, nullable) NSNumber * bitrate;
@property(nonatomic, strong, nullable) QualityFallbackStrategyBox * fallbackStrategy;
@end

@interface CupertinoVideoOptions : NSObject
+ (instancetype)makeWithFileType:(nullable CupertinoFileTypeBox *)fileType
    codec:(nullable CupertinoCodecTypeBox *)codec
    fps:(nullable NSNumber *)fps;
/// Specify video file type, defaults to [AVFileTypeQuickTimeMovie].
@property(nonatomic, strong, nullable) CupertinoFileTypeBox * fileType;
/// Specify video codec, defaults to [AVVideoCodecTypeH264].
@property(nonatomic, strong, nullable) CupertinoCodecTypeBox * codec;
/// Specify video fps, defaults to [30].
@property(nonatomic, strong, nullable) NSNumber * fps;
@end

@interface PigeonSensorTypeDevice : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithSensorType:(PigeonSensorType)sensorType
    name:(NSString *)name
    iso:(double )iso
    flashAvailable:(BOOL )flashAvailable
    uid:(NSString *)uid;
@property(nonatomic, assign) PigeonSensorType sensorType;
/// A localized device name for display in the user interface.
@property(nonatomic, copy) NSString * name;
/// The current exposure ISO value.
@property(nonatomic, assign) double  iso;
/// A Boolean value that indicates whether the flash is currently available for use.
@property(nonatomic, assign) BOOL  flashAvailable;
/// An identifier that uniquely identifies the device.
@property(nonatomic, copy) NSString * uid;
@end

@interface AndroidFocusSettings : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithAutoCancelDurationInMillis:(NSInteger )autoCancelDurationInMillis;
/// The auto focus will be canceled after the given [autoCancelDurationInMillis].
/// If [autoCancelDurationInMillis] is equals to 0 (or less), the auto focus
/// will **not** be canceled. A manual `focusOnPoint` call will be needed to
/// focus on an other point.
/// Minimal duration of [autoCancelDurationInMillis] is 1000 ms. If set
/// between 0 (exclusive) and 1000 (exclusive), it will be raised to 1000.
@property(nonatomic, assign) NSInteger  autoCancelDurationInMillis;
@end

@interface PlaneWrapper : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithBytes:(FlutterStandardTypedData *)bytes
    bytesPerRow:(NSInteger )bytesPerRow
    bytesPerPixel:(nullable NSNumber *)bytesPerPixel
    width:(nullable NSNumber *)width
    height:(nullable NSNumber *)height;
@property(nonatomic, strong) FlutterStandardTypedData * bytes;
@property(nonatomic, assign) NSInteger  bytesPerRow;
@property(nonatomic, strong, nullable) NSNumber * bytesPerPixel;
@property(nonatomic, strong, nullable) NSNumber * width;
@property(nonatomic, strong, nullable) NSNumber * height;
@end

@interface CropRectWrapper : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithLeft:(NSInteger )left
    top:(NSInteger )top
    width:(NSInteger )width
    height:(NSInteger )height;
@property(nonatomic, assign) NSInteger  left;
@property(nonatomic, assign) NSInteger  top;
@property(nonatomic, assign) NSInteger  width;
@property(nonatomic, assign) NSInteger  height;
@end

@interface AnalysisImageWrapper : NSObject
/// `init` unavailable to enforce nonnull fields, see the `make` class method.
- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)makeWithFormat:(AnalysisImageFormat)format
    bytes:(nullable FlutterStandardTypedData *)bytes
    width:(NSInteger )width
    height:(NSInteger )height
    planes:(nullable NSArray<PlaneWrapper *> *)planes
    cropRect:(nullable CropRectWrapper *)cropRect
    rotation:(nullable AnalysisRotationBox *)rotation;
@property(nonatomic, assign) AnalysisImageFormat format;
@property(nonatomic, strong, nullable) FlutterStandardTypedData * bytes;
@property(nonatomic, assign) NSInteger  width;
@property(nonatomic, assign) NSInteger  height;
@property(nonatomic, copy, nullable) NSArray<PlaneWrapper *> * planes;
@property(nonatomic, strong, nullable) CropRectWrapper * cropRect;
@property(nonatomic, strong, nullable) AnalysisRotationBox * rotation;
@end

/// The codec used by all APIs.
NSObject<FlutterMessageCodec> *nullGetPigeonCodec(void);

@protocol AnalysisImageUtils
- (void)nv21toJpegNv21Image:(AnalysisImageWrapper *)nv21Image jpegQuality:(NSInteger)jpegQuality completion:(void (^)(AnalysisImageWrapper *_Nullable, FlutterError *_Nullable))completion;
- (void)yuv420toJpegYuvImage:(AnalysisImageWrapper *)yuvImage jpegQuality:(NSInteger)jpegQuality completion:(void (^)(AnalysisImageWrapper *_Nullable, FlutterError *_Nullable))completion;
- (void)yuv420toNv21YuvImage:(AnalysisImageWrapper *)yuvImage completion:(void (^)(AnalysisImageWrapper *_Nullable, FlutterError *_Nullable))completion;
- (void)bgra8888toJpegBgra8888image:(AnalysisImageWrapper *)bgra8888image jpegQuality:(NSInteger)jpegQuality completion:(void (^)(AnalysisImageWrapper *_Nullable, FlutterError *_Nullable))completion;
@end

extern void SetUpAnalysisImageUtils(id<FlutterBinaryMessenger> binaryMessenger, NSObject<AnalysisImageUtils> *_Nullable api);

extern void SetUpAnalysisImageUtilsWithSuffix(id<FlutterBinaryMessenger> binaryMessenger, NSObject<AnalysisImageUtils> *_Nullable api, NSString *messageChannelSuffix);


@protocol CameraInterface
- (void)setupCameraSensors:(NSArray<PigeonSensor *> *)sensors aspectRatio:(NSString *)aspectRatio zoom:(double)zoom mirrorFrontCamera:(BOOL)mirrorFrontCamera enablePhysicalButton:(BOOL)enablePhysicalButton flashMode:(NSString *)flashMode captureMode:(NSString *)captureMode enableImageStream:(BOOL)enableImageStream exifPreferences:(ExifPreferences *)exifPreferences videoOptions:(nullable VideoOptions *)videoOptions completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// @return `nil` only when `error != nil`.
- (nullable NSArray<NSString *> *)checkPermissionsPermissions:(NSArray<NSString *> *)permissions error:(FlutterError *_Nullable *_Nonnull)error;
/// Returns given [CamerAwesomePermission] list (as String). Location permission might be
/// refused but the app should still be able to run.
- (void)requestPermissionsSaveGpsLocation:(BOOL)saveGpsLocation completion:(void (^)(NSArray<NSString *> *_Nullable, FlutterError *_Nullable))completion;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)getPreviewTextureIdCameraPosition:(NSInteger)cameraPosition error:(FlutterError *_Nullable *_Nonnull)error;
- (void)takePhotoSensors:(NSArray<PigeonSensor *> *)sensors paths:(NSArray<NSString *> *)paths completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
- (void)recordVideoSensors:(NSArray<PigeonSensor *> *)sensors paths:(NSArray<NSString *> *)paths completion:(void (^)(FlutterError *_Nullable))completion;
- (void)pauseVideoRecordingWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)resumeVideoRecordingWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)receivedImageFromStreamWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)stopRecordingVideoWithCompletion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// @return `nil` only when `error != nil`.
- (nullable NSArray<PigeonSensorTypeDevice *> *)getFrontSensorsWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSArray<PigeonSensorTypeDevice *> *)getBackSensorsWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)startWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)stopWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)setFlashModeMode:(NSString *)mode error:(FlutterError *_Nullable *_Nonnull)error;
- (void)handleAutoFocusWithError:(FlutterError *_Nullable *_Nonnull)error;
/// Starts auto focus on a point at ([x], [y]).
///
/// On Android, you can control after how much time you want to switch back
/// to passive focus mode with [androidFocusSettings].
- (void)focusOnPointPreviewSize:(PreviewSize *)previewSize x:(double)x y:(double)y androidFocusSettings:(nullable AndroidFocusSettings *)androidFocusSettings error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setZoomZoom:(double)zoom error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setMirrorFrontCameraMirror:(BOOL)mirror error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setSensorSensors:(NSArray<PigeonSensor *> *)sensors error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setCorrectionBrightness:(double)brightness error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setShutterSpeedShutterSpeedInSeconds:(double)shutterSpeedInSeconds error:(FlutterError *_Nullable *_Nonnull)error;
/// Set white balance manually with Kelvin temperature (2000K-9000K) or -1.0 for auto mode
- (void)setWhiteBalanceKelvinTemperature:(double)kelvinTemperature error:(FlutterError *_Nullable *_Nonnull)error;
/// Set ISO manually with standard values (100, 200, 400, 800, 1600, 3200, 6400) or -1.0 for auto mode
- (void)setISOIsoValue:(double)isoValue error:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)getMinZoomWithError:(FlutterError *_Nullable *_Nonnull)error;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)getMaxZoomWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)setCaptureModeMode:(NSString *)mode error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setRecordingAudioModeEnableAudio:(BOOL)enableAudio completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// @return `nil` only when `error != nil`.
- (nullable NSArray<PreviewSize *> *)availableSizesWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)refreshWithError:(FlutterError *_Nullable *_Nonnull)error;
- (nullable PreviewSize *)getEffectivPreviewSizeIndex:(NSInteger)index error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setPhotoSizeSize:(PreviewSize *)size error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setPreviewSizeSize:(PreviewSize *)size error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setAspectRatioAspectRatio:(NSString *)aspectRatio error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setupImageAnalysisStreamFormat:(NSString *)format width:(NSInteger)width maxFramesPerSecond:(nullable NSNumber *)maxFramesPerSecond autoStart:(BOOL)autoStart error:(FlutterError *_Nullable *_Nonnull)error;
- (void)setExifPreferencesExifPreferences:(ExifPreferences *)exifPreferences completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
- (void)startAnalysisWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)stopAnalysisWithError:(FlutterError *_Nullable *_Nonnull)error;
- (void)setFilterMatrix:(NSArray<double> *)matrix error:(FlutterError *_Nullable *_Nonnull)error;
- (void)isVideoRecordingAndImageAnalysisSupportedSensor:(PigeonSensorPosition)sensor completion:(void (^)(NSNumber *_Nullable, FlutterError *_Nullable))completion;
/// @return `nil` only when `error != nil`.
- (nullable NSNumber *)isMultiCamSupportedWithError:(FlutterError *_Nullable *_Nonnull)error;
@end

extern void SetUpCameraInterface(id<FlutterBinaryMessenger> binaryMessenger, NSObject<CameraInterface> *_Nullable api);

extern void SetUpCameraInterfaceWithSuffix(id<FlutterBinaryMessenger> binaryMessenger, NSObject<CameraInterface> *_Nullable api, NSString *messageChannelSuffix);

NS_ASSUME_NONNULL_END
