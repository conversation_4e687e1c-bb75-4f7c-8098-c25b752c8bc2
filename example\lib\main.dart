import 'dart:async'; // Import for StreamSubscription
import 'dart:io';

// import 'package:better_open_file/better_open_file.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/pigeon.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart'; // Import for getTemporaryDirectory
import 'utils/file_utils.dart';
import 'screens/gallery_screen.dart';
import 'screens/image_viewer_screen.dart';
import 'screens/video_player_screen.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_flash_button.dart' as awesome_flash_button; // Added alias
import 'package:camerawesome/src/widgets/buttons/awesome_aspect_ratio_button.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_location_button.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_capture_button.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_camera_switch_button.dart';

void main() {
  runApp(const CameraAwesomeApp());
}

class CameraAwesomeApp extends StatelessWidget {
  const CameraAwesomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'camerAwesome',
      home: CameraPage(),
    );
  }
}

class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  // ValueNotifiers to control the visibility of the sliders
  final ValueNotifier<bool> _showBlurSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showShutterSpeedSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showExposureSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showWhiteBalanceSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showISOSlider = ValueNotifier(false);

  // Add a StreamSubscription to listen to blur changes
  StreamSubscription<double>? _blurSubscription;

  // ValueNotifier for last captured image path
  final ValueNotifier<String?> _lastCapturedFilePathNotifier = ValueNotifier(null);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _blurSubscription?.cancel(); // Cancel subscription on dispose
    _showBlurSlider.dispose();
    _showShutterSpeedSlider.dispose();
    _showExposureSlider.dispose();
    _showWhiteBalanceSlider.dispose();
    _showISOSlider.dispose();
    _lastCapturedFilePathNotifier.dispose(); // Dispose the notifier
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: CameraAwesomeBuilder.awesome(
          onMediaCaptureEvent: (event) {
            switch ((event.status, event.isPicture, event.isVideo)) {
              case (MediaCaptureStatus.capturing, true, false):
                debugPrint('Capturing picture...');
              case (MediaCaptureStatus.success, true, false):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Picture saved: ${single.file?.path}');
                    _lastCapturedFilePathNotifier.value = single.file?.path; // Update notifier
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple image taken: $key ${value?.path}');
                      if (_lastCapturedFilePathNotifier.value == null) { // Store the first one if multiple
                        _lastCapturedFilePathNotifier.value = value?.path;
                      }
                    });
                  },
                );
              case (MediaCaptureStatus.failure, true, false):
                debugPrint('Failed to capture picture: ${event.exception}');
              case (MediaCaptureStatus.capturing, false, true):
                debugPrint('Capturing video...');
              case (MediaCaptureStatus.success, false, true):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Video saved: ${single.file?.path}');
                    _lastCapturedFilePathNotifier.value = single.file?.path; // Update notifier
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple video taken: $key ${value?.path}');
                      if (_lastCapturedFilePathNotifier.value == null) { // Store the first one if multiple
                        _lastCapturedFilePathNotifier.value = value?.path;
                      }
                    });
                  },
                );
              case (MediaCaptureStatus.failure, false, true):
                debugPrint('Failed to capture video: ${event.exception}');
              default:
                debugPrint('Unknown event: $event');
            }
          },
          saveConfig: SaveConfig.photoAndVideo(
            initialCaptureMode: CaptureMode.photo,
            photoPathBuilder: (sensors) async {
              final Directory extDir = await getTemporaryDirectory();
              final testDir = await Directory(
                '${extDir.path}/camerawesome',
              ).create(recursive: true);
              if (sensors.length == 1) {
                final String filePath =
                    '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                return SingleCaptureRequest(filePath, sensors.first);
              }
              // Separate pictures taken with front and back camera
              return MultipleCaptureRequest(
                {
                  for (final sensor in sensors)
                    sensor:
                        '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.jpg',
                },
              );
            },
            videoOptions: VideoOptions(
              enableAudio: true,
              ios: CupertinoVideoOptions(
                fps: 10,
              ),
              android: AndroidVideoOptions(
                bitrate: 6000000,
                fallbackStrategy: QualityFallbackStrategy.lower,
              ),
            ),
            exifPreferences: ExifPreferences(saveGPSLocation: true),
          ),
          sensorConfig: SensorConfig.single(
            sensor: Sensor.position(SensorPosition.back),
            flashMode: FlashMode.auto,
            aspectRatio: CameraAspectRatios.ratio_4_3,
            zoom: 0.0,
          ),
          enablePhysicalButton: true,
          // filter: AwesomeFilter.AddictiveRed,
          previewAlignment: Alignment.center,
          previewFit: CameraPreviewFit.contain,
          // Add manual exposure control to the default UI
          // The middle content will only contain the blur toggle and a spacer
          // Middle content is just a spacer now
          middleContentBuilder: (state) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.end, // Align to bottom
              mainAxisSize: MainAxisSize.min, // Occupy minimum space
              children: [
                // ISO slider panel
                AwesomeISOSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showISOSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.yellow,
                  sliderInactiveColor: Colors.yellow.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                // White Balance slider panel
                AwesomeWhiteBalanceSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showWhiteBalanceSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.cyan,
                  sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                AwesomeZoomSelector(state: state),
              ],
            );
          },
          // Top actions will contain all the buttons
          topActionsBuilder: (state) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Equally space all icons
                  children: [
                    // All icons in a single row
                    awesome_flash_button.AwesomeFlashButton(state: state),
                    AwesomeShutterSpeedSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showShutterSpeedSlider,
                      sliderActiveColor: Colors.orange,
                      sliderInactiveColor: Colors.orange.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    if (state is PhotoCameraState) // Conditional rendering
                      AwesomeAspectRatioButton(state: state as PhotoCameraState),
                    if (state is PhotoCameraState) // Conditional rendering
                      AwesomeLocationButton(state: state as PhotoCameraState),
                    AwesomeExposureSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showExposureSlider,
                      sliderActiveColor: Colors.white,
                      sliderInactiveColor: Colors.white.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    if (state is PhotoCameraState)
                      RawMaterialButton(
                        onPressed: () {
                          _showBlurSlider.value = !_showBlurSlider.value;
                        },
                        elevation: 0.0,
                        fillColor: Colors.transparent,
                        padding: const EdgeInsets.all(15.0),
                        shape: const CircleBorder(),
                        child: const Icon(
                          Icons.landscape,
                          size: 35.0,
                          color: Colors.white,
                        ),
                      ),
                    // White Balance Control button (re-added to top bar)
                    AwesomeWhiteBalanceSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showWhiteBalanceSlider,
                      sliderActiveColor: Colors.cyan,
                      sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    // ISO Control button
                    AwesomeISOSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showISOSlider,
                      sliderActiveColor: Colors.yellow,
                      sliderInactiveColor: Colors.yellow.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                  ],
                ),
              ),
            );
          },
          // Bottom actions will contain all the sliders and mode selectors, plus capture/switch buttons
          bottomActionsBuilder: (state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch, // Stretch to fill width
              children: [
                // Shutter speed slider panel
                AwesomeShutterSpeedSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showShutterSpeedSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.orange,
                  sliderInactiveColor: Colors.orange.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                // Exposure slider panel
                AwesomeExposureSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showExposureSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.white,
                  sliderInactiveColor: Colors.white.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                // Blur slider panel
                if (state is PhotoCameraState)
                  ValueListenableBuilder<bool>(
                    valueListenable: _showBlurSlider,
                    builder: (context, isVisible, child) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        height: isVisible ? 120 : 0, // Approximate height of blur slider
                        child: isVisible ? AwesomeBlurSelector(state: state) : null,
                      );
                    },
                  ),
                // Main controls section
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6), // More opaque background
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24.0),
                      topRight: Radius.circular(24.0),
                    ),
                  ),
                  padding: const EdgeInsets.only(top: 16.0, bottom: 24.0), // Increased vertical padding
                  child: Column(
                    children: [
                      // Camera Mode Selector
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: AwesomeCameraModeSelector(state: state),
                      ),
                      const SizedBox(height: 24.0), // More spacing
                      // Capture and Switch Buttons Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          AwesomeCameraSwitchButton(state: state),
                          AwesomeCaptureButton(state: state),
                          ValueListenableBuilder<String?>(
                            valueListenable: _lastCapturedFilePathNotifier,
                            builder: (context, path, child) {
                              if (path == null) {
                                return const SizedBox(width: 50, height: 50); // Placeholder for alignment
                              }
                              return GestureDetector(
                                onTap: () {
                                  // Navigate to gallery screen
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const GalleryScreen(),
                                    ),
                                  );
                                },
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Colors.white, width: 2.0), // Add a white border
                                  ),
                                  child: ClipOval(
                                    child: Image.file(
                                      File(path),
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
          onPreviewTapBuilder: (state) => OnPreviewTap(
            onTap: (position, flutterPreviewSize, pixelPreviewSize) {
              if (state is PhotoCameraState) {
                state.focusOnPoint(
                  flutterPosition: position,
                  pixelPreviewSize: pixelPreviewSize,
                  flutterPreviewSize: flutterPreviewSize,
                );
                // When tap-to-focus occurs, blur is reset, so hide all sliders
                _showBlurSlider.value = false;
                _showShutterSpeedSlider.value = false;
                _showExposureSlider.value = false;
              }
            },
          ),
          onMediaTap: (mediaCapture) {
            mediaCapture.captureRequest.when(
              single: (single) {
                debugPrint('single: ${single.file?.path}');
                single.file?.open();
              },
              multiple: (multiple) {
                multiple.fileBySensor.forEach((key, value) {
                  debugPrint('multiple file taken: $key ${value?.path}');
                  value?.open();
                });
              },
            );
          },
          availableFilters: awesomePresetFiltersList,
        ),
      ),
    );
  }
}
