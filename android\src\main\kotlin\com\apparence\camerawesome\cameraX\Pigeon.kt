// Autogenerated from <PERSON><PERSON> (v21.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon
@file:Suppress("UNCHECKED_CAST", "ArrayInDataClass")

package com.apparence.camerawesome.cameraX

import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  return if (exception is FlutterError) {
    listOf(
      exception.code,
      exception.message,
      exception.details
    )
  } else {
    listOf(
      exception.javaClass.simpleName,
      exception.toString(),
      "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
    )
  }
}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class FlutterError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()

enum class PigeonSensorPosition(val raw: Int) {
  BACK(0),
  FRONT(1),
  UNKNOWN(2);

  companion object {
    fun ofRaw(raw: Int): PigeonSensorPosition? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
 * let the device choose the best/worst quality available.
 * [highest] is the default quality.
 *
 * Qualities are defined like this:
 * [sd] < [hd] < [fhd] < [uhd]
 */
enum class VideoRecordingQuality(val raw: Int) {
  LOWEST(0),
  SD(1),
  HD(2),
  FHD(3),
  UHD(4),
  HIGHEST(5);

  companion object {
    fun ofRaw(raw: Int): VideoRecordingQuality? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * If the specified [VideoRecordingQuality] is not available on the device,
 * the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
 * [higher] is the default fallback strategy.
 */
enum class QualityFallbackStrategy(val raw: Int) {
  HIGHER(0),
  LOWER(1);

  companion object {
    fun ofRaw(raw: Int): QualityFallbackStrategy? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CupertinoFileType(val raw: Int) {
  QUICK_TIME_MOVIE(0),
  MPEG4(1),
  APPLE_M4V(2),
  TYPE3GPP(3),
  TYPE3GPP2(4);

  companion object {
    fun ofRaw(raw: Int): CupertinoFileType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CupertinoCodecType(val raw: Int) {
  H264(0),
  HEVC(1),
  HEVC_WITH_ALPHA(2),
  JPEG(3),
  APPLE_PRO_RES4444(4),
  APPLE_PRO_RES422(5),
  APPLE_PRO_RES422HQ(6),
  APPLE_PRO_RES422LT(7),
  APPLE_PRO_RES422PROXY(8);

  companion object {
    fun ofRaw(raw: Int): CupertinoCodecType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class PigeonSensorType(val raw: Int) {
  /**
   * A built-in wide-angle camera.
   *
   * The wide angle sensor is the default sensor for iOS
   */
  WIDE_ANGLE(0),
  /** A built-in camera with a shorter focal length than that of the wide-angle camera. */
  ULTRA_WIDE_ANGLE(1),
  /** A built-in camera device with a longer focal length than the wide-angle camera. */
  TELEPHOTO(2),
  /**
   * A device that consists of two cameras, one Infrared and one YUV.
   *
   * iOS only
   */
  TRUE_DEPTH(3),
  UNKNOWN(4);

  companion object {
    fun ofRaw(raw: Int): PigeonSensorType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CamerAwesomePermission(val raw: Int) {
  STORAGE(0),
  CAMERA(1),
  LOCATION(2),
  RECORD_AUDIO(3);

  companion object {
    fun ofRaw(raw: Int): CamerAwesomePermission? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class AnalysisImageFormat(val raw: Int) {
  YUV_420(0),
  BGRA8888(1),
  JPEG(2),
  NV21(3),
  UNKNOWN(4);

  companion object {
    fun ofRaw(raw: Int): AnalysisImageFormat? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class AnalysisRotation(val raw: Int) {
  ROTATION0DEG(0),
  ROTATION90DEG(1),
  ROTATION180DEG(2),
  ROTATION270DEG(3);

  companion object {
    fun ofRaw(raw: Int): AnalysisRotation? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PreviewSize (
  val width: Double,
  val height: Double
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): PreviewSize {
      val width = pigeonVar_list[0] as Double
      val height = pigeonVar_list[1] as Double
      return PreviewSize(width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class ExifPreferences (
  val saveGPSLocation: Boolean
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): ExifPreferences {
      val saveGPSLocation = pigeonVar_list[0] as Boolean
      return ExifPreferences(saveGPSLocation)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      saveGPSLocation,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PigeonSensor (
  val position: PigeonSensorPosition,
  val type: PigeonSensorType,
  val deviceId: String? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): PigeonSensor {
      val position = pigeonVar_list[0] as PigeonSensorPosition
      val type = pigeonVar_list[1] as PigeonSensorType
      val deviceId = pigeonVar_list[2] as String?
      return PigeonSensor(position, type, deviceId)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      position,
      type,
      deviceId,
    )
  }
}

/**
 * Video recording options. Some of them are specific to each platform.
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class VideoOptions (
  /** Enable audio while video recording */
  val enableAudio: Boolean,
  /** The quality of the video recording, defaults to [VideoRecordingQuality.highest]. */
  val quality: VideoRecordingQuality? = null,
  val android: AndroidVideoOptions? = null,
  val ios: CupertinoVideoOptions? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): VideoOptions {
      val enableAudio = pigeonVar_list[0] as Boolean
      val quality = pigeonVar_list[1] as VideoRecordingQuality?
      val android = pigeonVar_list[2] as AndroidVideoOptions?
      val ios = pigeonVar_list[3] as CupertinoVideoOptions?
      return VideoOptions(enableAudio, quality, android, ios)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      enableAudio,
      quality,
      android,
      ios,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AndroidVideoOptions (
  /**
   * The bitrate of the video recording. Only set it if a custom bitrate is
   * desired.
   */
  val bitrate: Long? = null,
  val fallbackStrategy: QualityFallbackStrategy? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): AndroidVideoOptions {
      val bitrate = pigeonVar_list[0].let { num -> if (num is Int) num.toLong() else num as Long? }
      val fallbackStrategy = pigeonVar_list[1] as QualityFallbackStrategy?
      return AndroidVideoOptions(bitrate, fallbackStrategy)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      bitrate,
      fallbackStrategy,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class CupertinoVideoOptions (
  /** Specify video file type, defaults to [AVFileTypeQuickTimeMovie]. */
  val fileType: CupertinoFileType? = null,
  /** Specify video codec, defaults to [AVVideoCodecTypeH264]. */
  val codec: CupertinoCodecType? = null,
  /** Specify video fps, defaults to [30]. */
  val fps: Long? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CupertinoVideoOptions {
      val fileType = pigeonVar_list[0] as CupertinoFileType?
      val codec = pigeonVar_list[1] as CupertinoCodecType?
      val fps = pigeonVar_list[2].let { num -> if (num is Int) num.toLong() else num as Long? }
      return CupertinoVideoOptions(fileType, codec, fps)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      fileType,
      codec,
      fps,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PigeonSensorTypeDevice (
  val sensorType: PigeonSensorType,
  /** A localized device name for display in the user interface. */
  val name: String,
  /** The current exposure ISO value. */
  val iso: Double,
  /** A Boolean value that indicates whether the flash is currently available for use. */
  val flashAvailable: Boolean,
  /** An identifier that uniquely identifies the device. */
  val uid: String
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): PigeonSensorTypeDevice {
      val sensorType = pigeonVar_list[0] as PigeonSensorType
      val name = pigeonVar_list[1] as String
      val iso = pigeonVar_list[2] as Double
      val flashAvailable = pigeonVar_list[3] as Boolean
      val uid = pigeonVar_list[4] as String
      return PigeonSensorTypeDevice(sensorType, name, iso, flashAvailable, uid)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      sensorType,
      name,
      iso,
      flashAvailable,
      uid,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AndroidFocusSettings (
  /**
   * The auto focus will be canceled after the given [autoCancelDurationInMillis].
   * If [autoCancelDurationInMillis] is equals to 0 (or less), the auto focus
   * will **not** be canceled. A manual `focusOnPoint` call will be needed to
   * focus on an other point.
   * Minimal duration of [autoCancelDurationInMillis] is 1000 ms. If set
   * between 0 (exclusive) and 1000 (exclusive), it will be raised to 1000.
   */
  val autoCancelDurationInMillis: Long
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): AndroidFocusSettings {
      val autoCancelDurationInMillis = pigeonVar_list[0].let { num -> if (num is Int) num.toLong() else num as Long }
      return AndroidFocusSettings(autoCancelDurationInMillis)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      autoCancelDurationInMillis,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PlaneWrapper (
  val bytes: ByteArray,
  val bytesPerRow: Long,
  val bytesPerPixel: Long? = null,
  val width: Long? = null,
  val height: Long? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): PlaneWrapper {
      val bytes = pigeonVar_list[0] as ByteArray
      val bytesPerRow = pigeonVar_list[1].let { num -> if (num is Int) num.toLong() else num as Long }
      val bytesPerPixel = pigeonVar_list[2].let { num -> if (num is Int) num.toLong() else num as Long? }
      val width = pigeonVar_list[3].let { num -> if (num is Int) num.toLong() else num as Long? }
      val height = pigeonVar_list[4].let { num -> if (num is Int) num.toLong() else num as Long? }
      return PlaneWrapper(bytes, bytesPerRow, bytesPerPixel, width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      bytes,
      bytesPerRow,
      bytesPerPixel,
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class CropRectWrapper (
  val left: Long,
  val top: Long,
  val width: Long,
  val height: Long
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): CropRectWrapper {
      val left = pigeonVar_list[0].let { num -> if (num is Int) num.toLong() else num as Long }
      val top = pigeonVar_list[1].let { num -> if (num is Int) num.toLong() else num as Long }
      val width = pigeonVar_list[2].let { num -> if (num is Int) num.toLong() else num as Long }
      val height = pigeonVar_list[3].let { num -> if (num is Int) num.toLong() else num as Long }
      return CropRectWrapper(left, top, width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      left,
      top,
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AnalysisImageWrapper (
  val format: AnalysisImageFormat,
  val bytes: ByteArray? = null,
  val width: Long,
  val height: Long,
  val planes: List<PlaneWrapper?>? = null,
  val cropRect: CropRectWrapper? = null,
  val rotation: AnalysisRotation? = null
)
 {
  companion object {
    fun fromList(pigeonVar_list: List<Any?>): AnalysisImageWrapper {
      val format = pigeonVar_list[0] as AnalysisImageFormat
      val bytes = pigeonVar_list[1] as ByteArray?
      val width = pigeonVar_list[2].let { num -> if (num is Int) num.toLong() else num as Long }
      val height = pigeonVar_list[3].let { num -> if (num is Int) num.toLong() else num as Long }
      val planes = pigeonVar_list[4] as List<PlaneWrapper?>?
      val cropRect = pigeonVar_list[5] as CropRectWrapper?
      val rotation = pigeonVar_list[6] as AnalysisRotation?
      return AnalysisImageWrapper(format, bytes, width, height, planes, cropRect, rotation)
    }
  }
  fun toList(): List<Any?> {
    return listOf(
      format,
      bytes,
      width,
      height,
      planes,
      cropRect,
      rotation,
    )
  }
}
private object PigeonPigeonCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      129.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          PigeonSensorPosition.ofRaw(it)
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          VideoRecordingQuality.ofRaw(it)
        }
      }
      131.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          QualityFallbackStrategy.ofRaw(it)
        }
      }
      132.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          CupertinoFileType.ofRaw(it)
        }
      }
      133.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          CupertinoCodecType.ofRaw(it)
        }
      }
      134.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          PigeonSensorType.ofRaw(it)
        }
      }
      135.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          CamerAwesomePermission.ofRaw(it)
        }
      }
      136.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          AnalysisImageFormat.ofRaw(it)
        }
      }
      137.toByte() -> {
        return (readValue(buffer) as Int?)?.let {
          AnalysisRotation.ofRaw(it)
        }
      }
      138.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PreviewSize.fromList(it)
        }
      }
      139.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          ExifPreferences.fromList(it)
        }
      }
      140.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PigeonSensor.fromList(it)
        }
      }
      141.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          VideoOptions.fromList(it)
        }
      }
      142.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AndroidVideoOptions.fromList(it)
        }
      }
      143.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CupertinoVideoOptions.fromList(it)
        }
      }
      144.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PigeonSensorTypeDevice.fromList(it)
        }
      }
      145.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AndroidFocusSettings.fromList(it)
        }
      }
      146.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PlaneWrapper.fromList(it)
        }
      }
      147.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CropRectWrapper.fromList(it)
        }
      }
      148.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AnalysisImageWrapper.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is PigeonSensorPosition -> {
        stream.write(129)
        writeValue(stream, value.raw)
      }
      is VideoRecordingQuality -> {
        stream.write(130)
        writeValue(stream, value.raw)
      }
      is QualityFallbackStrategy -> {
        stream.write(131)
        writeValue(stream, value.raw)
      }
      is CupertinoFileType -> {
        stream.write(132)
        writeValue(stream, value.raw)
      }
      is CupertinoCodecType -> {
        stream.write(133)
        writeValue(stream, value.raw)
      }
      is PigeonSensorType -> {
        stream.write(134)
        writeValue(stream, value.raw)
      }
      is CamerAwesomePermission -> {
        stream.write(135)
        writeValue(stream, value.raw)
      }
      is AnalysisImageFormat -> {
        stream.write(136)
        writeValue(stream, value.raw)
      }
      is AnalysisRotation -> {
        stream.write(137)
        writeValue(stream, value.raw)
      }
      is PreviewSize -> {
        stream.write(138)
        writeValue(stream, value.toList())
      }
      is ExifPreferences -> {
        stream.write(139)
        writeValue(stream, value.toList())
      }
      is PigeonSensor -> {
        stream.write(140)
        writeValue(stream, value.toList())
      }
      is VideoOptions -> {
        stream.write(141)
        writeValue(stream, value.toList())
      }
      is AndroidVideoOptions -> {
        stream.write(142)
        writeValue(stream, value.toList())
      }
      is CupertinoVideoOptions -> {
        stream.write(143)
        writeValue(stream, value.toList())
      }
      is PigeonSensorTypeDevice -> {
        stream.write(144)
        writeValue(stream, value.toList())
      }
      is AndroidFocusSettings -> {
        stream.write(145)
        writeValue(stream, value.toList())
      }
      is PlaneWrapper -> {
        stream.write(146)
        writeValue(stream, value.toList())
      }
      is CropRectWrapper -> {
        stream.write(147)
        writeValue(stream, value.toList())
      }
      is AnalysisImageWrapper -> {
        stream.write(148)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}


/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface AnalysisImageUtils {
  fun nv21toJpeg(nv21Image: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun yuv420toJpeg(yuvImage: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun yuv420toNv21(yuvImage: AnalysisImageWrapper, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun bgra8888toJpeg(bgra8888image: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)

  companion object {
    /** The codec used by AnalysisImageUtils. */
    val codec: MessageCodec<Any?> by lazy {
      PigeonPigeonCodec
    }
    /** Sets up an instance of `AnalysisImageUtils` to handle messages through the `binaryMessenger`. */
    @JvmOverloads
    fun setUp(binaryMessenger: BinaryMessenger, api: AnalysisImageUtils?, messageChannelSuffix: String = "") {
      val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.AnalysisImageUtils.nv21toJpeg$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val nv21ImageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { num -> if (num is Int) num.toLong() else num as Long }
            api.nv21toJpeg(nv21ImageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toJpeg$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val yuvImageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { num -> if (num is Int) num.toLong() else num as Long }
            api.yuv420toJpeg(yuvImageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toNv21$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val yuvImageArg = args[0] as AnalysisImageWrapper
            api.yuv420toNv21(yuvImageArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.AnalysisImageUtils.bgra8888toJpeg$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val bgra8888imageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { num -> if (num is Int) num.toLong() else num as Long }
            api.bgra8888toJpeg(bgra8888imageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface CameraInterface {
  fun setupCamera(sensors: List<PigeonSensor>, aspectRatio: String, zoom: Double, mirrorFrontCamera: Boolean, enablePhysicalButton: Boolean, flashMode: String, captureMode: String, enableImageStream: Boolean, exifPreferences: ExifPreferences, videoOptions: VideoOptions?, callback: (Result<Boolean>) -> Unit)
  fun checkPermissions(permissions: List<String>): List<String>
  /**
   * Returns given [CamerAwesomePermission] list (as String). Location permission might be
   * refused but the app should still be able to run.
   */
  fun requestPermissions(saveGpsLocation: Boolean, callback: (Result<List<String>>) -> Unit)
  fun getPreviewTextureId(cameraPosition: Long): Long
  fun takePhoto(sensors: List<PigeonSensor>, paths: List<String?>, callback: (Result<Boolean>) -> Unit)
  fun recordVideo(sensors: List<PigeonSensor>, paths: List<String?>, callback: (Result<Unit>) -> Unit)
  fun pauseVideoRecording()
  fun resumeVideoRecording()
  fun receivedImageFromStream()
  fun stopRecordingVideo(callback: (Result<Boolean>) -> Unit)
  fun getFrontSensors(): List<PigeonSensorTypeDevice>
  fun getBackSensors(): List<PigeonSensorTypeDevice>
  fun start(): Boolean
  fun stop(): Boolean
  fun setFlashMode(mode: String)
  fun handleAutoFocus()
  /**
   * Starts auto focus on a point at ([x], [y]).
   *
   * On Android, you can control after how much time you want to switch back
   * to passive focus mode with [androidFocusSettings].
   */
  fun focusOnPoint(previewSize: PreviewSize, x: Double, y: Double, androidFocusSettings: AndroidFocusSettings?)
  fun setZoom(zoom: Double)
  fun setMirrorFrontCamera(mirror: Boolean)
  fun setSensor(sensors: List<PigeonSensor>)
  fun setCorrection(brightness: Double)
  fun setShutterSpeed(shutterSpeedInSeconds: Double)
  /** Set white balance manually with Kelvin temperature (2000K-9000K) or -1.0 for auto mode */
  fun setWhiteBalance(kelvinTemperature: Double)
  /** Set ISO manually with standard values (100, 200, 400, 800, 1600, 3200, 6400) or -1.0 for auto mode */
  fun setISO(isoValue: Double)
  fun getMinZoom(): Double
  fun getMaxZoom(): Double
  fun setCaptureMode(mode: String)
  fun setRecordingAudioMode(enableAudio: Boolean, callback: (Result<Boolean>) -> Unit)
  fun availableSizes(): List<PreviewSize>
  fun refresh()
  fun getEffectivPreviewSize(index: Long): PreviewSize?
  fun setPhotoSize(size: PreviewSize)
  fun setPreviewSize(size: PreviewSize)
  fun setAspectRatio(aspectRatio: String)
  fun setupImageAnalysisStream(format: String, width: Long, maxFramesPerSecond: Double?, autoStart: Boolean)
  fun setExifPreferences(exifPreferences: ExifPreferences, callback: (Result<Boolean>) -> Unit)
  fun startAnalysis()
  fun stopAnalysis()
  fun setFilter(matrix: List<Double>)
  fun isVideoRecordingAndImageAnalysisSupported(sensor: PigeonSensorPosition, callback: (Result<Boolean>) -> Unit)
  fun isMultiCamSupported(): Boolean

  companion object {
    /** The codec used by CameraInterface. */
    val codec: MessageCodec<Any?> by lazy {
      PigeonPigeonCodec
    }
    /** Sets up an instance of `CameraInterface` to handle messages through the `binaryMessenger`. */
    @JvmOverloads
    fun setUp(binaryMessenger: BinaryMessenger, api: CameraInterface?, messageChannelSuffix: String = "") {
      val separatedMessageChannelSuffix = if (messageChannelSuffix.isNotEmpty()) ".$messageChannelSuffix" else ""
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setupCamera$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val aspectRatioArg = args[1] as String
            val zoomArg = args[2] as Double
            val mirrorFrontCameraArg = args[3] as Boolean
            val enablePhysicalButtonArg = args[4] as Boolean
            val flashModeArg = args[5] as String
            val captureModeArg = args[6] as String
            val enableImageStreamArg = args[7] as Boolean
            val exifPreferencesArg = args[8] as ExifPreferences
            val videoOptionsArg = args[9] as VideoOptions?
            api.setupCamera(sensorsArg, aspectRatioArg, zoomArg, mirrorFrontCameraArg, enablePhysicalButtonArg, flashModeArg, captureModeArg, enableImageStreamArg, exifPreferencesArg, videoOptionsArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.checkPermissions$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val permissionsArg = args[0] as List<String>
            val wrapped: List<Any?> = try {
              listOf(api.checkPermissions(permissionsArg))
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.requestPermissions$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val saveGpsLocationArg = args[0] as Boolean
            api.requestPermissions(saveGpsLocationArg) { result: Result<List<String>> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getPreviewTextureId$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val cameraPositionArg = args[0].let { num -> if (num is Int) num.toLong() else num as Long }
            val wrapped: List<Any?> = try {
              listOf(api.getPreviewTextureId(cameraPositionArg))
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.takePhoto$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val pathsArg = args[1] as List<String?>
            api.takePhoto(sensorsArg, pathsArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.recordVideo$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val pathsArg = args[1] as List<String?>
            api.recordVideo(sensorsArg, pathsArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.pauseVideoRecording$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.pauseVideoRecording()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.resumeVideoRecording$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.resumeVideoRecording()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.receivedImageFromStream$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.receivedImageFromStream()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.stopRecordingVideo$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.stopRecordingVideo{ result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getFrontSensors$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.getFrontSensors())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getBackSensors$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.getBackSensors())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.start$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.start())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.stop$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.stop())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setFlashMode$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val modeArg = args[0] as String
            val wrapped: List<Any?> = try {
              api.setFlashMode(modeArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.handleAutoFocus$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.handleAutoFocus()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.focusOnPoint$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val previewSizeArg = args[0] as PreviewSize
            val xArg = args[1] as Double
            val yArg = args[2] as Double
            val androidFocusSettingsArg = args[3] as AndroidFocusSettings?
            val wrapped: List<Any?> = try {
              api.focusOnPoint(previewSizeArg, xArg, yArg, androidFocusSettingsArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setZoom$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val zoomArg = args[0] as Double
            val wrapped: List<Any?> = try {
              api.setZoom(zoomArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setMirrorFrontCamera$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val mirrorArg = args[0] as Boolean
            val wrapped: List<Any?> = try {
              api.setMirrorFrontCamera(mirrorArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setSensor$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val wrapped: List<Any?> = try {
              api.setSensor(sensorsArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setCorrection$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val brightnessArg = args[0] as Double
            val wrapped: List<Any?> = try {
              api.setCorrection(brightnessArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setShutterSpeed$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val shutterSpeedInSecondsArg = args[0] as Double
            val wrapped: List<Any?> = try {
              api.setShutterSpeed(shutterSpeedInSecondsArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setWhiteBalance$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val kelvinTemperatureArg = args[0] as Double
            val wrapped: List<Any?> = try {
              api.setWhiteBalance(kelvinTemperatureArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setISO$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val isoValueArg = args[0] as Double
            val wrapped: List<Any?> = try {
              api.setISO(isoValueArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getMinZoom$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.getMinZoom())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getMaxZoom$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.getMaxZoom())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setCaptureMode$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val modeArg = args[0] as String
            val wrapped: List<Any?> = try {
              api.setCaptureMode(modeArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setRecordingAudioMode$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val enableAudioArg = args[0] as Boolean
            api.setRecordingAudioMode(enableAudioArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.availableSizes$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.availableSizes())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.refresh$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.refresh()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.getEffectivPreviewSize$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val indexArg = args[0].let { num -> if (num is Int) num.toLong() else num as Long }
            val wrapped: List<Any?> = try {
              listOf(api.getEffectivPreviewSize(indexArg))
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setPhotoSize$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sizeArg = args[0] as PreviewSize
            val wrapped: List<Any?> = try {
              api.setPhotoSize(sizeArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setPreviewSize$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sizeArg = args[0] as PreviewSize
            val wrapped: List<Any?> = try {
              api.setPreviewSize(sizeArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setAspectRatio$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val aspectRatioArg = args[0] as String
            val wrapped: List<Any?> = try {
              api.setAspectRatio(aspectRatioArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setupImageAnalysisStream$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val formatArg = args[0] as String
            val widthArg = args[1].let { num -> if (num is Int) num.toLong() else num as Long }
            val maxFramesPerSecondArg = args[2] as Double?
            val autoStartArg = args[3] as Boolean
            val wrapped: List<Any?> = try {
              api.setupImageAnalysisStream(formatArg, widthArg, maxFramesPerSecondArg, autoStartArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setExifPreferences$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val exifPreferencesArg = args[0] as ExifPreferences
            api.setExifPreferences(exifPreferencesArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.startAnalysis$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.startAnalysis()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.stopAnalysis$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              api.stopAnalysis()
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.setFilter$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val matrixArg = args[0] as List<Double>
            val wrapped: List<Any?> = try {
              api.setFilter(matrixArg)
              listOf(null)
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.isVideoRecordingAndImageAnalysisSupported$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorArg = args[0] as PigeonSensorPosition
            api.isVideoRecordingAndImageAnalysisSupported(sensorArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.camerawesome.CameraInterface.isMultiCamSupported$separatedMessageChannelSuffix", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            val wrapped: List<Any?> = try {
              listOf(api.isMultiCamSupported())
            } catch (exception: Throwable) {
              wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
