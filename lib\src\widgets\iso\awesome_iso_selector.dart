import 'dart:async';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// ISO preset values for professional photography
enum ISOValue {
  auto(-1.0, 'AUTO', Icons.auto_mode),
  iso100(100.0, '100', Icons.iso),
  iso200(200.0, '200', Icons.iso),
  iso400(400.0, '400', Icons.iso),
  iso800(800.0, '800', Icons.iso),
  iso1600(1600.0, '1600', Icons.iso),
  iso3200(3200.0, '3200', Icons.iso),
  iso6400(6400.0, '6400', Icons.iso);

  const ISOValue(this.value, this.label, this.icon);
  
  final double value;
  final String label;
  final IconData icon;

  static ISOValue fromValue(double value) {
    return ISOValue.values.firstWhere(
      (iso) => iso.value == value,
      orElse: () => ISOValue.auto,
    );
  }

  bool get isAuto => value == -1.0;
}

/// A professional ISO control panel for camera applications
/// 
/// Features:
/// - Auto/Manual ISO toggle
/// - Slider for ISO value selection (100-6400)
/// - Real-time ISO value display
/// - Smooth animations and haptic feedback
/// - Consistent styling with existing CamerAwesome selectors
class AwesomeISOSelector extends StatefulWidget {
  final CameraState state;
  final bool showResetButton;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final ValueNotifier<bool>? visibilityNotifier;
  final bool showButton;
  final EdgeInsets padding;
  final bool showLabel;

  const AwesomeISOSelector({
    super.key,
    required this.state,
    this.showResetButton = true,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.visibilityNotifier,
    this.showButton = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    this.showLabel = true,
  });

  @override
  State<AwesomeISOSelector> createState() => _AwesomeISOSelectorState();
}

class _AwesomeISOSelectorState extends State<AwesomeISOSelector> {
  int _currentIndex = 0; // Default to auto mode
  Timer? _debounceTimer;
  
  // Use a local ValueNotifier if none is provided externally
  late final ValueNotifier<bool> _internalVisibilityNotifier;

  @override
  void initState() {
    super.initState();
    
    // Initialize with current ISO from state
    final currentISO = widget.state.iso;
    _currentIndex = _getIndexFromISO(currentISO);

    _internalVisibilityNotifier = widget.visibilityNotifier ?? ValueNotifier(false);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    // Only dispose the internal notifier if it was created by this widget
    if (widget.visibilityNotifier == null) {
      _internalVisibilityNotifier.dispose();
    }
    super.dispose();
  }

  int _getIndexFromISO(double isoValue) {
    final isoEnum = ISOValue.fromValue(isoValue);
    return ISOValue.values.indexOf(isoEnum);
  }

  void _onISOChanged(double sliderValue) {
    final newIndex = sliderValue.round();
    
    setState(() {
      _currentIndex = newIndex;
    });

    // Provide haptic feedback
    HapticFeedback.selectionClick();

    // Debounce the actual state update to prevent excessive processing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        final isoValue = ISOValue.values[_currentIndex];
        widget.state.setISO(isoValue.value);
      }
    });
  }

  void _resetToAuto() {
    setState(() {
      _currentIndex = 0; // Auto mode
    });
    widget.state.setISO(-1.0);
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showButton) {
      // Only show the panel
      return ValueListenableBuilder<bool>(
        valueListenable: _internalVisibilityNotifier,
        builder: (context, isVisible, child) {
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: isVisible ? 120 : 0,
            child: isVisible ? _buildISOPanel() : null,
          );
        },
      );
    }

    // Show both button and panel
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // ISO Button
        _buildISOButton(),
        // ISO Panel
        ValueListenableBuilder<bool>(
          valueListenable: _internalVisibilityNotifier,
          builder: (context, isVisible, child) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: isVisible ? 120 : 0,
              child: isVisible ? _buildISOPanel() : null,
            );
          },
        ),
      ],
    );
  }

  Widget _buildISOButton() {
    return StreamBuilder<double>(
      stream: widget.state.iso$,
      builder: (context, snapshot) {
        final currentISO = snapshot.data ?? -1.0;
        final isoValue = ISOValue.fromValue(currentISO);
        
        return GestureDetector(
          onTap: () {
            _internalVisibilityNotifier.value = !_internalVisibilityNotifier.value;
            HapticFeedback.selectionClick();
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.iso,
                  color: widget.textColor ?? Colors.white,
                  size: 24,
                ),
                const SizedBox(height: 2),
                Text(
                  isoValue.label,
                  style: TextStyle(
                    color: widget.textColor ?? Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildISOPanel() {
    return Container(
      margin: widget.padding,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showLabel)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'ISO',
                  style: TextStyle(
                    color: widget.textColor ?? Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (widget.showResetButton)
                  GestureDetector(
                    onTap: _resetToAuto,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'AUTO',
                        style: TextStyle(
                          color: widget.textColor ?? Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          const SizedBox(height: 12),
          // ISO Value Display
          Text(
            ISOValue.values[_currentIndex].label,
            style: TextStyle(
              color: widget.textColor ?? Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          // ISO Slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: widget.sliderActiveColor ?? Colors.white,
              inactiveTrackColor: widget.sliderInactiveColor ?? Colors.white.withOpacity(0.3),
              thumbColor: widget.sliderActiveColor ?? Colors.white,
              overlayColor: (widget.sliderActiveColor ?? Colors.white).withOpacity(0.2),
              trackHeight: 4,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            ),
            child: Slider(
              value: _currentIndex.toDouble(),
              min: 0,
              max: (ISOValue.values.length - 1).toDouble(),
              divisions: ISOValue.values.length - 1,
              onChanged: _onISOChanged,
            ),
          ),
        ],
      ),
    );
  }
}
