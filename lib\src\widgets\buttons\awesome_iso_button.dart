import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/src/widgets/utils/awesome_circle_icon.dart';
import 'package:camerawesome/src/widgets/utils/awesome_theme.dart';
import 'package:flutter/material.dart';

class AwesomeISOButton extends StatelessWidget {
  final CameraState state;
  final AwesomeTheme? theme;
  final Widget Function() iconBuilder;
  final void Function(CameraState) onISOTap;

  AwesomeISOButton({
    super.key,
    required this.state,
    this.theme,
    Widget Function()? iconBuilder,
    void Function(CameraState)? onISOTap,
    double scale = 1.3,
  })  : iconBuilder = iconBuilder ??
            (() {
              return StreamBuilder<double>(
                stream: state.iso$,
                builder: (context, snapshot) {
                  final currentISO = snapshot.data ?? -1.0;
                  final isAuto = currentISO == -1.0;
                  
                  return AwesomeCircleWidget(
                    theme: theme,
                    scale: scale,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.iso,
                          color: theme?.buttonTheme.foregroundColor ?? Colors.white,
                          size: 20,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          isAuto ? 'AUTO' : currentISO.toInt().toString(),
                          style: TextStyle(
                            color: theme?.buttonTheme.foregroundColor ?? Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }),
        onISOTap = onISOTap ?? ((state) => {});

  @override
  Widget build(BuildContext context) {
    final theme = this.theme ?? AwesomeThemeProvider.of(context).theme;

    return theme.buttonTheme.buttonBuilder(
      iconBuilder(),
      () => onISOTap(state),
    );
  }
}
