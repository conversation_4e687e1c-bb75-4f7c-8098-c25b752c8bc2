// Autogenerated from <PERSON><PERSON> (v21.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:typed_data' show Float64List, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';

PlatformException _createConnectionError(String channelName) {
  return PlatformException(
    code: 'channel-error',
    message: 'Unable to establish connection on channel: "$channelName".',
  );
}

enum PigeonSensorPosition {
  back,
  front,
  unknown,
}

/// Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
/// let the device choose the best/worst quality available.
/// [highest] is the default quality.
///
/// Qualities are defined like this:
/// [sd] < [hd] < [fhd] < [uhd]
enum VideoRecordingQuality {
  lowest,
  sd,
  hd,
  fhd,
  uhd,
  highest,
}

/// If the specified [VideoRecordingQuality] is not available on the device,
/// the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
/// [higher] is the default fallback strategy.
enum QualityFallbackStrategy {
  higher,
  lower,
}

enum CupertinoFileType {
  quickTimeMovie,
  mpeg4,
  appleM4V,
  type3GPP,
  type3GPP2,
}

enum CupertinoCodecType {
  h264,
  hevc,
  hevcWithAlpha,
  jpeg,
  appleProRes4444,
  appleProRes422,
  appleProRes422HQ,
  appleProRes422LT,
  appleProRes422Proxy,
}

enum PigeonSensorType {
  /// A built-in wide-angle camera.
  ///
  /// The wide angle sensor is the default sensor for iOS
  wideAngle,
  /// A built-in camera with a shorter focal length than that of the wide-angle camera.
  ultraWideAngle,
  /// A built-in camera device with a longer focal length than the wide-angle camera.
  telephoto,
  /// A device that consists of two cameras, one Infrared and one YUV.
  ///
  /// iOS only
  trueDepth,
  unknown,
}

enum CamerAwesomePermission {
  storage,
  camera,
  location,
  record_audio,
}

enum AnalysisImageFormat {
  yuv_420,
  bgra8888,
  jpeg,
  nv21,
  unknown,
}

enum AnalysisRotation {
  rotation0deg,
  rotation90deg,
  rotation180deg,
  rotation270deg,
}

class PreviewSize {
  PreviewSize({
    required this.width,
    required this.height,
  });

  double width;

  double height;

  Object encode() {
    return <Object?>[
      width,
      height,
    ];
  }

  static PreviewSize decode(Object result) {
    result as List<Object?>;
    return PreviewSize(
      width: result[0]! as double,
      height: result[1]! as double,
    );
  }
}

class ExifPreferences {
  ExifPreferences({
    required this.saveGPSLocation,
  });

  bool saveGPSLocation;

  Object encode() {
    return <Object?>[
      saveGPSLocation,
    ];
  }

  static ExifPreferences decode(Object result) {
    result as List<Object?>;
    return ExifPreferences(
      saveGPSLocation: result[0]! as bool,
    );
  }
}

class PigeonSensor {
  PigeonSensor({
    required this.position,
    required this.type,
    this.deviceId,
  });

  PigeonSensorPosition position;

  PigeonSensorType type;

  String? deviceId;

  Object encode() {
    return <Object?>[
      position,
      type,
      deviceId,
    ];
  }

  static PigeonSensor decode(Object result) {
    result as List<Object?>;
    return PigeonSensor(
      position: result[0]! as PigeonSensorPosition,
      type: result[1]! as PigeonSensorType,
      deviceId: result[2] as String?,
    );
  }
}

/// Video recording options. Some of them are specific to each platform.
class VideoOptions {
  VideoOptions({
    required this.enableAudio,
    this.quality,
    this.android,
    this.ios,
  });

  /// Enable audio while video recording
  bool enableAudio;

  /// The quality of the video recording, defaults to [VideoRecordingQuality.highest].
  VideoRecordingQuality? quality;

  AndroidVideoOptions? android;

  CupertinoVideoOptions? ios;

  Object encode() {
    return <Object?>[
      enableAudio,
      quality,
      android,
      ios,
    ];
  }

  static VideoOptions decode(Object result) {
    result as List<Object?>;
    return VideoOptions(
      enableAudio: result[0]! as bool,
      quality: result[1] as VideoRecordingQuality?,
      android: result[2] as AndroidVideoOptions?,
      ios: result[3] as CupertinoVideoOptions?,
    );
  }
}

class AndroidVideoOptions {
  AndroidVideoOptions({
    this.bitrate,
    this.fallbackStrategy,
  });

  /// The bitrate of the video recording. Only set it if a custom bitrate is
  /// desired.
  int? bitrate;

  QualityFallbackStrategy? fallbackStrategy;

  Object encode() {
    return <Object?>[
      bitrate,
      fallbackStrategy,
    ];
  }

  static AndroidVideoOptions decode(Object result) {
    result as List<Object?>;
    return AndroidVideoOptions(
      bitrate: result[0] as int?,
      fallbackStrategy: result[1] as QualityFallbackStrategy?,
    );
  }
}

class CupertinoVideoOptions {
  CupertinoVideoOptions({
    this.fileType,
    this.codec,
    this.fps,
  });

  /// Specify video file type, defaults to [AVFileTypeQuickTimeMovie].
  CupertinoFileType? fileType;

  /// Specify video codec, defaults to [AVVideoCodecTypeH264].
  CupertinoCodecType? codec;

  /// Specify video fps, defaults to [30].
  int? fps;

  Object encode() {
    return <Object?>[
      fileType,
      codec,
      fps,
    ];
  }

  static CupertinoVideoOptions decode(Object result) {
    result as List<Object?>;
    return CupertinoVideoOptions(
      fileType: result[0] as CupertinoFileType?,
      codec: result[1] as CupertinoCodecType?,
      fps: result[2] as int?,
    );
  }
}

class PigeonSensorTypeDevice {
  PigeonSensorTypeDevice({
    required this.sensorType,
    required this.name,
    required this.iso,
    required this.flashAvailable,
    required this.uid,
  });

  PigeonSensorType sensorType;

  /// A localized device name for display in the user interface.
  String name;

  /// The current exposure ISO value.
  double iso;

  /// A Boolean value that indicates whether the flash is currently available for use.
  bool flashAvailable;

  /// An identifier that uniquely identifies the device.
  String uid;

  Object encode() {
    return <Object?>[
      sensorType,
      name,
      iso,
      flashAvailable,
      uid,
    ];
  }

  static PigeonSensorTypeDevice decode(Object result) {
    result as List<Object?>;
    return PigeonSensorTypeDevice(
      sensorType: result[0]! as PigeonSensorType,
      name: result[1]! as String,
      iso: result[2]! as double,
      flashAvailable: result[3]! as bool,
      uid: result[4]! as String,
    );
  }
}

class AndroidFocusSettings {
  AndroidFocusSettings({
    required this.autoCancelDurationInMillis,
  });

  /// The auto focus will be canceled after the given [autoCancelDurationInMillis].
  /// If [autoCancelDurationInMillis] is equals to 0 (or less), the auto focus
  /// will **not** be canceled. A manual `focusOnPoint` call will be needed to
  /// focus on an other point.
  /// Minimal duration of [autoCancelDurationInMillis] is 1000 ms. If set
  /// between 0 (exclusive) and 1000 (exclusive), it will be raised to 1000.
  int autoCancelDurationInMillis;

  Object encode() {
    return <Object?>[
      autoCancelDurationInMillis,
    ];
  }

  static AndroidFocusSettings decode(Object result) {
    result as List<Object?>;
    return AndroidFocusSettings(
      autoCancelDurationInMillis: result[0]! as int,
    );
  }
}

class PlaneWrapper {
  PlaneWrapper({
    required this.bytes,
    required this.bytesPerRow,
    this.bytesPerPixel,
    this.width,
    this.height,
  });

  Uint8List bytes;

  int bytesPerRow;

  int? bytesPerPixel;

  int? width;

  int? height;

  Object encode() {
    return <Object?>[
      bytes,
      bytesPerRow,
      bytesPerPixel,
      width,
      height,
    ];
  }

  static PlaneWrapper decode(Object result) {
    result as List<Object?>;
    return PlaneWrapper(
      bytes: result[0]! as Uint8List,
      bytesPerRow: result[1]! as int,
      bytesPerPixel: result[2] as int?,
      width: result[3] as int?,
      height: result[4] as int?,
    );
  }
}

class CropRectWrapper {
  CropRectWrapper({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });

  int left;

  int top;

  int width;

  int height;

  Object encode() {
    return <Object?>[
      left,
      top,
      width,
      height,
    ];
  }

  static CropRectWrapper decode(Object result) {
    result as List<Object?>;
    return CropRectWrapper(
      left: result[0]! as int,
      top: result[1]! as int,
      width: result[2]! as int,
      height: result[3]! as int,
    );
  }
}

class AnalysisImageWrapper {
  AnalysisImageWrapper({
    required this.format,
    this.bytes,
    required this.width,
    required this.height,
    this.planes,
    this.cropRect,
    this.rotation,
  });

  AnalysisImageFormat format;

  Uint8List? bytes;

  int width;

  int height;

  List<PlaneWrapper?>? planes;

  CropRectWrapper? cropRect;

  AnalysisRotation? rotation;

  Object encode() {
    return <Object?>[
      format,
      bytes,
      width,
      height,
      planes,
      cropRect,
      rotation,
    ];
  }

  static AnalysisImageWrapper decode(Object result) {
    result as List<Object?>;
    return AnalysisImageWrapper(
      format: result[0]! as AnalysisImageFormat,
      bytes: result[1] as Uint8List?,
      width: result[2]! as int,
      height: result[3]! as int,
      planes: (result[4] as List<Object?>?)?.cast<PlaneWrapper?>(),
      cropRect: result[5] as CropRectWrapper?,
      rotation: result[6] as AnalysisRotation?,
    );
  }
}


class _PigeonCodec extends StandardMessageCodec {
  const _PigeonCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is PigeonSensorPosition) {
      buffer.putUint8(129);
      writeValue(buffer, value.index);
    } else     if (value is VideoRecordingQuality) {
      buffer.putUint8(130);
      writeValue(buffer, value.index);
    } else     if (value is QualityFallbackStrategy) {
      buffer.putUint8(131);
      writeValue(buffer, value.index);
    } else     if (value is CupertinoFileType) {
      buffer.putUint8(132);
      writeValue(buffer, value.index);
    } else     if (value is CupertinoCodecType) {
      buffer.putUint8(133);
      writeValue(buffer, value.index);
    } else     if (value is PigeonSensorType) {
      buffer.putUint8(134);
      writeValue(buffer, value.index);
    } else     if (value is CamerAwesomePermission) {
      buffer.putUint8(135);
      writeValue(buffer, value.index);
    } else     if (value is AnalysisImageFormat) {
      buffer.putUint8(136);
      writeValue(buffer, value.index);
    } else     if (value is AnalysisRotation) {
      buffer.putUint8(137);
      writeValue(buffer, value.index);
    } else     if (value is PreviewSize) {
      buffer.putUint8(138);
      writeValue(buffer, value.encode());
    } else     if (value is ExifPreferences) {
      buffer.putUint8(139);
      writeValue(buffer, value.encode());
    } else     if (value is PigeonSensor) {
      buffer.putUint8(140);
      writeValue(buffer, value.encode());
    } else     if (value is VideoOptions) {
      buffer.putUint8(141);
      writeValue(buffer, value.encode());
    } else     if (value is AndroidVideoOptions) {
      buffer.putUint8(142);
      writeValue(buffer, value.encode());
    } else     if (value is CupertinoVideoOptions) {
      buffer.putUint8(143);
      writeValue(buffer, value.encode());
    } else     if (value is PigeonSensorTypeDevice) {
      buffer.putUint8(144);
      writeValue(buffer, value.encode());
    } else     if (value is AndroidFocusSettings) {
      buffer.putUint8(145);
      writeValue(buffer, value.encode());
    } else     if (value is PlaneWrapper) {
      buffer.putUint8(146);
      writeValue(buffer, value.encode());
    } else     if (value is CropRectWrapper) {
      buffer.putUint8(147);
      writeValue(buffer, value.encode());
    } else     if (value is AnalysisImageWrapper) {
      buffer.putUint8(148);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 129: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : PigeonSensorPosition.values[value];
      case 130: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : VideoRecordingQuality.values[value];
      case 131: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : QualityFallbackStrategy.values[value];
      case 132: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CupertinoFileType.values[value];
      case 133: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CupertinoCodecType.values[value];
      case 134: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : PigeonSensorType.values[value];
      case 135: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : CamerAwesomePermission.values[value];
      case 136: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : AnalysisImageFormat.values[value];
      case 137: 
        final int? value = readValue(buffer) as int?;
        return value == null ? null : AnalysisRotation.values[value];
      case 138: 
        return PreviewSize.decode(readValue(buffer)!);
      case 139: 
        return ExifPreferences.decode(readValue(buffer)!);
      case 140: 
        return PigeonSensor.decode(readValue(buffer)!);
      case 141: 
        return VideoOptions.decode(readValue(buffer)!);
      case 142: 
        return AndroidVideoOptions.decode(readValue(buffer)!);
      case 143: 
        return CupertinoVideoOptions.decode(readValue(buffer)!);
      case 144: 
        return PigeonSensorTypeDevice.decode(readValue(buffer)!);
      case 145: 
        return AndroidFocusSettings.decode(readValue(buffer)!);
      case 146: 
        return PlaneWrapper.decode(readValue(buffer)!);
      case 147: 
        return CropRectWrapper.decode(readValue(buffer)!);
      case 148: 
        return AnalysisImageWrapper.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

class AnalysisImageUtils {
  /// Constructor for [AnalysisImageUtils].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  AnalysisImageUtils({BinaryMessenger? binaryMessenger, String messageChannelSuffix = ''})
      : pigeonVar_binaryMessenger = binaryMessenger,
        pigeonVar_messageChannelSuffix = messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
  final BinaryMessenger? pigeonVar_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  final String pigeonVar_messageChannelSuffix;

  Future<AnalysisImageWrapper> nv21toJpeg(AnalysisImageWrapper nv21Image, int jpegQuality) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.AnalysisImageUtils.nv21toJpeg$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[nv21Image, jpegQuality]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> yuv420toJpeg(AnalysisImageWrapper yuvImage, int jpegQuality) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toJpeg$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[yuvImage, jpegQuality]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> yuv420toNv21(AnalysisImageWrapper yuvImage) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toNv21$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[yuvImage]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> bgra8888toJpeg(AnalysisImageWrapper bgra8888image, int jpegQuality) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.AnalysisImageUtils.bgra8888toJpeg$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[bgra8888image, jpegQuality]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as AnalysisImageWrapper?)!;
    }
  }
}

class CameraInterface {
  /// Constructor for [CameraInterface].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  CameraInterface({BinaryMessenger? binaryMessenger, String messageChannelSuffix = ''})
      : pigeonVar_binaryMessenger = binaryMessenger,
        pigeonVar_messageChannelSuffix = messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
  final BinaryMessenger? pigeonVar_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  final String pigeonVar_messageChannelSuffix;

  Future<bool> setupCamera(List<PigeonSensor?> sensors, String aspectRatio, double zoom, bool mirrorFrontCamera, bool enablePhysicalButton, String flashMode, String captureMode, bool enableImageStream, ExifPreferences exifPreferences, VideoOptions? videoOptions) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setupCamera$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[sensors, aspectRatio, zoom, mirrorFrontCamera, enablePhysicalButton, flashMode, captureMode, enableImageStream, exifPreferences, videoOptions]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<List<String?>> checkPermissions(List<String?> permissions) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.checkPermissions$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[permissions]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<String?>();
    }
  }

  /// Returns given [CamerAwesomePermission] list (as String). Location permission might be
  /// refused but the app should still be able to run.
  Future<List<String?>> requestPermissions(bool saveGpsLocation) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.requestPermissions$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[saveGpsLocation]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<String?>();
    }
  }

  Future<int> getPreviewTextureId(int cameraPosition) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getPreviewTextureId$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[cameraPosition]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as int?)!;
    }
  }

  Future<bool> takePhoto(List<PigeonSensor?> sensors, List<String?> paths) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.takePhoto$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[sensors, paths]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<void> recordVideo(List<PigeonSensor?> sensors, List<String?> paths) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.recordVideo$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[sensors, paths]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> pauseVideoRecording() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.pauseVideoRecording$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> resumeVideoRecording() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.resumeVideoRecording$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> receivedImageFromStream() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.receivedImageFromStream$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> stopRecordingVideo() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.stopRecordingVideo$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<List<PigeonSensorTypeDevice?>> getFrontSensors() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getFrontSensors$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<PigeonSensorTypeDevice?>();
    }
  }

  Future<List<PigeonSensorTypeDevice?>> getBackSensors() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getBackSensors$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<PigeonSensorTypeDevice?>();
    }
  }

  Future<bool> start() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.start$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<bool> stop() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.stop$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<void> setFlashMode(String mode) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setFlashMode$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[mode]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> handleAutoFocus() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.handleAutoFocus$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Starts auto focus on a point at ([x], [y]).
  ///
  /// On Android, you can control after how much time you want to switch back
  /// to passive focus mode with [androidFocusSettings].
  Future<void> focusOnPoint(PreviewSize previewSize, double x, double y, AndroidFocusSettings? androidFocusSettings) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.focusOnPoint$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[previewSize, x, y, androidFocusSettings]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setZoom(double zoom) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setZoom$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[zoom]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setMirrorFrontCamera(bool mirror) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setMirrorFrontCamera$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[mirror]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setSensor(List<PigeonSensor?> sensors) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setSensor$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[sensors]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setCorrection(double brightness) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setCorrection$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[brightness]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setShutterSpeed(double shutterSpeedInSeconds) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setShutterSpeed$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[shutterSpeedInSeconds]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Set white balance manually with Kelvin temperature (2000K-9000K) or -1.0 for auto mode
  Future<void> setWhiteBalance(double kelvinTemperature) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setWhiteBalance$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[kelvinTemperature]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  /// Set ISO manually with standard values (100, 200, 400, 800, 1600, 3200, 6400) or -1.0 for auto mode
  Future<void> setISO(double isoValue) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setISO$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[isoValue]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<double> getMinZoom() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getMinZoom$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as double?)!;
    }
  }

  Future<double> getMaxZoom() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getMaxZoom$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as double?)!;
    }
  }

  Future<void> setCaptureMode(String mode) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setCaptureMode$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[mode]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> setRecordingAudioMode(bool enableAudio) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setRecordingAudioMode$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[enableAudio]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<List<PreviewSize?>> availableSizes() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.availableSizes$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<PreviewSize?>();
    }
  }

  Future<void> refresh() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.refresh$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<PreviewSize?> getEffectivPreviewSize(int index) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.getEffectivPreviewSize$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[index]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return (pigeonVar_replyList[0] as PreviewSize?);
    }
  }

  Future<void> setPhotoSize(PreviewSize size) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setPhotoSize$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[size]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setPreviewSize(PreviewSize size) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setPreviewSize$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[size]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setAspectRatio(String aspectRatio) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setAspectRatio$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[aspectRatio]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setupImageAnalysisStream(String format, int width, double? maxFramesPerSecond, bool autoStart) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setupImageAnalysisStream$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[format, width, maxFramesPerSecond, autoStart]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> setExifPreferences(ExifPreferences exifPreferences) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setExifPreferences$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[exifPreferences]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<void> startAnalysis() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.startAnalysis$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> stopAnalysis() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.stopAnalysis$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setFilter(List<double?> matrix) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.setFilter$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[matrix]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> isVideoRecordingAndImageAnalysisSupported(PigeonSensorPosition sensor) async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.isVideoRecordingAndImageAnalysisSupported$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(<Object?>[sensor]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }

  Future<bool> isMultiCamSupported() async {
    final String pigeonVar_channelName = 'dev.flutter.pigeon.camerawesome.CameraInterface.isMultiCamSupported$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel = BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList =
        await pigeonVar_channel.send(null) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as bool?)!;
    }
  }
}
